<script setup lang="ts">
import PromptCard from "@/components/PromptCard.vue";
import { Clock, Sparkles, TrendingUp } from "lucide-vue-next";
import { ref } from "vue";

// Mock data for prompts
const trendingPrompts = ref([
  {
    id: 1,
    title: "Professional Portrait Photography",
    content:
      "Create a professional headshot of a [profession] in [setting] with [lighting style]...",
    author: "PhotoMaster",
    model: "Midjourney",
    votes: 1247,
    thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop",
    tags: ["photography", "portrait", "professional"],
    isPremium: false,
  },
  {
    id: 2,
    title: "Creative Writing Assistant",
    content: "You are a creative writing assistant. Help me write a [genre] story about [topic]...",
    author: "WriterBot",
    model: "ChatGPT",
    votes: 892,
    thumbnail: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop",
    tags: ["writing", "creative", "storytelling"],
    isPremium: true,
    price: 4.99,
  },
  {
    id: 3,
    title: "Futuristic City Concept",
    content:
      "Design a futuristic cityscape with [architectural style] and [technology elements]...",
    author: "CityDesigner",
    model: "Midjourney",
    votes: 756,
    thumbnail: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop",
    tags: ["architecture", "futuristic", "concept"],
    isPremium: false,
  },
]);

const newestPrompts = ref([
  {
    id: 4,
    title: "AI Video Script Generator",
    content:
      "Generate a compelling video script for [platform] about [topic] targeting [audience]...",
    author: "VideoCreator",
    model: "ChatGPT",
    votes: 234,
    thumbnail: "https://images.unsplash.com/photo-1536240478700-b869070f9279?w=300&h=200&fit=crop",
    tags: ["video", "script", "content"],
    isPremium: true,
    price: 2.99,
  },
  {
    id: 5,
    title: "Abstract Art Generator",
    content:
      "Create an abstract artwork with [color palette] and [artistic movement] influences...",
    author: "AbstractArtist",
    model: "Midjourney",
    votes: 189,
    thumbnail: "https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=200&fit=crop",
    tags: ["abstract", "art", "creative"],
    isPremium: false,
  },
  {
    id: 6,
    title: "Product Description Writer",
    content:
      "Write compelling product descriptions for [product type] highlighting [key features]...",
    author: "MarketingPro",
    model: "ChatGPT",
    votes: 156,
    thumbnail: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=200&fit=crop",
    tags: ["marketing", "product", "copywriting"],
    isPremium: false,
  },
]);
</script>

<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="relative py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto text-center">
        <div class="relative">
          <h1 class="text-5xl md:text-7xl font-sora font-bold mb-6">
            <span class="gradient-text">Discover</span>
            <br />
            <span class="text-white">AI Prompts</span>
          </h1>
          <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Explore, vote, and sell the best prompts for ChatGPT, Midjourney, and Sora. Join the
            community of AI creators.
          </p>

          <!-- Floating elements -->
          <div
            class="absolute -top-10 -left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-float"
          ></div>
          <div
            class="absolute -bottom-10 -right-10 w-32 h-32 bg-cyan-500/20 rounded-full blur-xl animate-float"
            style="animation-delay: 2s"
          ></div>
        </div>
      </div>
    </section>

    <!-- Trending Prompts Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center space-x-3 mb-8">
          <TrendingUp class="w-6 h-6 text-purple-400" />
          <h2 class="text-3xl font-sora font-bold text-white">Trending Prompts</h2>
          <Sparkles class="w-5 h-5 text-cyan-400" />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <PromptCard v-for="prompt in trendingPrompts" :key="prompt.id" :prompt="prompt" />
        </div>
      </div>
    </section>

    <!-- Newest Prompts Section -->
    <section class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center space-x-3 mb-8">
          <Clock class="w-6 h-6 text-cyan-400" />
          <h2 class="text-3xl font-sora font-bold text-white">Newest Prompts</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <PromptCard v-for="prompt in newestPrompts" :key="prompt.id" :prompt="prompt" />
        </div>
      </div>
    </section>
  </div>
</template>
