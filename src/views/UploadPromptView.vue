<script setup lang="ts">
import { ref } from 'vue'
import { Upload, Image, DollarSign, Tag, Type, FileText } from 'lucide-vue-next'

const form = ref({
  title: '',
  content: '',
  model: 'ChatGPT',
  tags: '',
  isPremium: false,
  price: '',
  previewImage: null as File | null
})

const previewImageUrl = ref('')
const isSubmitting = ref(false)

const models = [
  'ChatGPT',
  'Midjourney',
  'Sora',
  'Claude',
  'Gemini'
]

const handleImageUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    form.value.previewImage = file
    const reader = new FileReader()
    reader.onload = (e) => {
      previewImageUrl.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const removeImage = () => {
  form.value.previewImage = null
  previewImageUrl.value = ''
}

const submitPrompt = async () => {
  isSubmitting.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // Reset form
  form.value = {
    title: '',
    content: '',
    model: 'ChatGPT',
    tags: '',
    isPremium: false,
    price: '',
    previewImage: null
  }
  previewImageUrl.value = ''
  isSubmitting.value = false
  
  // Show success message (you could use a toast library here)
  alert('Prompt uploaded successfully!')
}

const tagSuggestions = [
  'photography', 'writing', 'art', 'business', 'marketing', 'creative', 
  'professional', 'design', 'coding', 'education', 'entertainment', 'lifestyle'
]
</script>

<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-3xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-sora font-bold mb-4">
          <span class="gradient-text">Upload</span>
          <span class="text-white"> Your Prompt</span>
        </h1>
        <p class="text-gray-300 text-lg">
          Share your amazing AI prompts with the community
        </p>
      </div>

      <!-- Upload Form -->
      <form @submit.prevent="submitPrompt" class="space-y-8">
        <!-- Title -->
        <div class="glass-card p-6">
          <label class="flex items-center space-x-2 text-white font-medium mb-3">
            <Type class="w-5 h-5 text-purple-400" />
            <span>Prompt Title</span>
          </label>
          <input
            v-model="form.title"
            type="text"
            placeholder="Enter a catchy title for your prompt..."
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
            required
          />
        </div>

        <!-- Content -->
        <div class="glass-card p-6">
          <label class="flex items-center space-x-2 text-white font-medium mb-3">
            <FileText class="w-5 h-5 text-cyan-400" />
            <span>Prompt Content</span>
          </label>
          <textarea
            v-model="form.content"
            placeholder="Enter your full prompt here. Use [brackets] for customizable parameters..."
            rows="8"
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm resize-none"
            required
          ></textarea>
          <p class="text-gray-400 text-sm mt-2">
            Tip: Use [brackets] for parameters that users can customize, like [style], [color], [mood], etc.
          </p>
        </div>

        <!-- Model Selection -->
        <div class="glass-card p-6">
          <label class="flex items-center space-x-2 text-white font-medium mb-3">
            <span class="w-5 h-5 bg-gradient-to-r from-purple-400 to-cyan-400 rounded"></span>
            <span>AI Model</span>
          </label>
          <select
            v-model="form.model"
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
            required
          >
            <option v-for="model in models" :key="model" :value="model" class="bg-gray-800">
              {{ model }}
            </option>
          </select>
        </div>

        <!-- Preview Image Upload -->
        <div class="glass-card p-6">
          <label class="flex items-center space-x-2 text-white font-medium mb-3">
            <Image class="w-5 h-5 text-green-400" />
            <span>Preview Image</span>
          </label>
          
          <div v-if="!previewImageUrl" class="border-2 border-dashed border-white/20 rounded-lg p-8 text-center">
            <Upload class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-400 mb-4">Upload a preview image for your prompt</p>
            <label class="glass-button cursor-pointer">
              <span>Choose Image</span>
              <input
                type="file"
                accept="image/*"
                @change="handleImageUpload"
                class="hidden"
              />
            </label>
          </div>
          
          <div v-else class="relative">
            <img
              :src="previewImageUrl"
              alt="Preview"
              class="w-full h-48 object-cover rounded-lg"
            />
            <button
              type="button"
              @click="removeImage"
              class="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-2 transition-colors duration-200"
            >
              ×
            </button>
          </div>
        </div>

        <!-- Tags -->
        <div class="glass-card p-6">
          <label class="flex items-center space-x-2 text-white font-medium mb-3">
            <Tag class="w-5 h-5 text-yellow-400" />
            <span>Tags</span>
          </label>
          <input
            v-model="form.tags"
            type="text"
            placeholder="photography, portrait, professional (comma-separated)"
            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
          />
          
          <!-- Tag Suggestions -->
          <div class="mt-3">
            <p class="text-gray-400 text-sm mb-2">Suggested tags:</p>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="tag in tagSuggestions"
                :key="tag"
                type="button"
                @click="form.tags = form.tags ? form.tags + ', ' + tag : tag"
                class="px-3 py-1 text-sm bg-white/10 text-gray-300 rounded-full hover:bg-white/20 transition-colors duration-200"
              >
                #{{ tag }}
              </button>
            </div>
          </div>
        </div>

        <!-- Premium Options -->
        <div class="glass-card p-6">
          <div class="flex items-center space-x-3 mb-4">
            <input
              v-model="form.isPremium"
              type="checkbox"
              id="premium"
              class="w-4 h-4 text-purple-600 bg-white/10 border-white/20 rounded focus:ring-purple-500"
            />
            <label for="premium" class="flex items-center space-x-2 text-white font-medium">
              <DollarSign class="w-5 h-5 text-yellow-400" />
              <span>Make this a premium prompt</span>
            </label>
          </div>
          
          <div v-if="form.isPremium" class="mt-4">
            <label class="block text-white font-medium mb-2">Price (USD)</label>
            <input
              v-model="form.price"
              type="number"
              step="0.01"
              min="0.99"
              placeholder="4.99"
              class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
        </div>

        <!-- Submit Button -->
        <div class="text-center">
          <button
            type="submit"
            :disabled="isSubmitting"
            class="bg-gradient-to-r from-purple-500 to-cyan-500 hover:from-purple-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed px-8 py-4 rounded-lg text-white font-medium text-lg transition-all duration-300 transform hover:scale-105"
          >
            <span v-if="isSubmitting">Uploading...</span>
            <span v-else>Upload Prompt</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>
