<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Heart, Copy, ArrowUp, ArrowDown, Share2, DollarSign, Check } from 'lucide-vue-next'

const route = useRoute()
const promptId = computed(() => route.params.id)

// Mock prompt data
const prompt = ref({
  id: 1,
  title: "Professional Portrait Photography",
  content: `Create a professional headshot of a [profession] in [setting] with [lighting style]. The subject should be wearing [attire] and have a [expression] expression. Use [camera angle] and ensure the background is [background description]. The lighting should be [lighting description] to create a [mood] atmosphere.

Parameters to customize:
- [profession]: doctor, lawyer, entrepreneur, artist, etc.
- [setting]: modern office, studio, outdoor location, etc.
- [lighting style]: natural light, studio lighting, golden hour, etc.
- [attire]: business suit, casual professional, creative outfit, etc.
- [expression]: confident, approachable, serious, friendly, etc.
- [camera angle]: eye level, slightly above, close-up, medium shot, etc.
- [background description]: blurred office, plain white, urban setting, etc.
- [lighting description]: soft and even, dramatic side lighting, bright and airy, etc.
- [mood]: professional, creative, approachable, authoritative, etc.

Example: "Create a professional headshot of a doctor in a modern medical office with natural lighting. The subject should be wearing a white coat and have a confident expression. Use eye level camera angle and ensure the background is a blurred medical office. The lighting should be soft and even to create a trustworthy atmosphere."`,
  author: "PhotoMaster",
  model: "Midjourney",
  votes: 1247,
  userVote: null, // null, 'up', or 'down'
  thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop",
  previewImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop",
  tags: ["photography", "portrait", "professional", "headshot", "business"],
  isPremium: false,
  price: null,
  createdAt: "2024-01-15",
  downloads: 892
})

const isLiked = ref(false)
const isCopied = ref(false)

const vote = (type: 'up' | 'down') => {
  if (prompt.value.userVote === type) {
    // Remove vote
    prompt.value.userVote = null
    prompt.value.votes += type === 'up' ? -1 : 1
  } else {
    // Add or change vote
    if (prompt.value.userVote) {
      prompt.value.votes += prompt.value.userVote === 'up' ? -2 : 2
    } else {
      prompt.value.votes += type === 'up' ? 1 : -1
    }
    prompt.value.userVote = type
  }
}

const copyPrompt = async () => {
  try {
    await navigator.clipboard.writeText(prompt.value.content)
    isCopied.value = true
    setTimeout(() => {
      isCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
  }
}

const toggleLike = () => {
  isLiked.value = !isLiked.value
}

const sharePrompt = async () => {
  if (navigator.share) {
    try {
      await navigator.share({
        title: prompt.value.title,
        text: `Check out this amazing AI prompt: ${prompt.value.title}`,
        url: window.location.href,
      })
    } catch (err) {
      console.error('Error sharing:', err)
    }
  } else {
    // Fallback to copying URL
    await navigator.clipboard.writeText(window.location.href)
  }
}
</script>

<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <!-- Header -->
      <div class="glass-card p-8 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
          <!-- Preview Image -->
          <div class="lg:w-1/2 mb-6 lg:mb-0">
            <div class="relative overflow-hidden rounded-xl">
              <img 
                :src="prompt.previewImage" 
                :alt="prompt.title"
                class="w-full h-64 lg:h-80 object-cover"
              />
              
              <!-- Model Badge -->
              <div class="absolute top-4 left-4">
                <span class="bg-purple-500 px-3 py-1 text-sm font-medium text-white rounded-full">
                  {{ prompt.model }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- Info -->
          <div class="lg:w-1/2 space-y-6">
            <div>
              <h1 class="text-3xl font-sora font-bold text-white mb-2">
                {{ prompt.title }}
              </h1>
              <div class="flex items-center space-x-4 text-gray-400">
                <span>by {{ prompt.author }}</span>
                <span>•</span>
                <span>{{ prompt.downloads }} downloads</span>
                <span>•</span>
                <span>{{ prompt.createdAt }}</span>
              </div>
            </div>
            
            <!-- Tags -->
            <div class="flex flex-wrap gap-2">
              <span 
                v-for="tag in prompt.tags" 
                :key="tag"
                class="px-3 py-1 text-sm bg-white/10 text-gray-300 rounded-full"
              >
                #{{ tag }}
              </span>
            </div>
            
            <!-- Actions -->
            <div class="flex flex-wrap gap-3">
              <!-- Vote buttons -->
              <div class="flex items-center space-x-1 glass-card px-3 py-2">
                <button 
                  @click="vote('up')"
                  :class="[
                    'p-1 rounded transition-colors duration-200',
                    prompt.userVote === 'up' ? 'text-green-400' : 'text-gray-400 hover:text-green-400'
                  ]"
                >
                  <ArrowUp class="w-5 h-5" />
                </button>
                <span class="text-white font-medium">{{ prompt.votes }}</span>
                <button 
                  @click="vote('down')"
                  :class="[
                    'p-1 rounded transition-colors duration-200',
                    prompt.userVote === 'down' ? 'text-red-400' : 'text-gray-400 hover:text-red-400'
                  ]"
                >
                  <ArrowDown class="w-5 h-5" />
                </button>
              </div>
              
              <!-- Copy button -->
              <button 
                @click="copyPrompt"
                class="glass-button flex items-center space-x-2"
              >
                <Check v-if="isCopied" class="w-4 h-4 text-green-400" />
                <Copy v-else class="w-4 h-4" />
                <span>{{ isCopied ? 'Copied!' : 'Copy' }}</span>
              </button>
              
              <!-- Like button -->
              <button 
                @click="toggleLike"
                :class="[
                  'glass-button flex items-center space-x-2',
                  isLiked ? 'text-red-400' : ''
                ]"
              >
                <Heart :class="isLiked ? 'fill-current' : ''" class="w-4 h-4" />
                <span>{{ isLiked ? 'Liked' : 'Like' }}</span>
              </button>
              
              <!-- Share button -->
              <button 
                @click="sharePrompt"
                class="glass-button flex items-center space-x-2"
              >
                <Share2 class="w-4 h-4" />
                <span>Share</span>
              </button>
              
              <!-- Buy button (if premium) -->
              <button 
                v-if="prompt.isPremium"
                class="bg-gradient-to-r from-purple-500 to-cyan-500 hover:from-purple-600 hover:to-cyan-600 px-6 py-2 rounded-lg text-white font-medium transition-all duration-300 flex items-center space-x-2"
              >
                <DollarSign class="w-4 h-4" />
                <span>Buy for ${{ prompt.price }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Prompt Content -->
      <div class="glass-card p-8">
        <h2 class="text-2xl font-sora font-bold text-white mb-6">Full Prompt</h2>
        <div class="bg-black/30 rounded-lg p-6 border border-white/10">
          <pre class="text-gray-300 whitespace-pre-wrap font-mono text-sm leading-relaxed">{{ prompt.content }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>
