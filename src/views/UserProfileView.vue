<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import PromptCard from '@/components/PromptCard.vue'
import { User, MapPin, Calendar, Award, Heart, Download } from 'lucide-vue-next'

const route = useRoute()
const username = computed(() => route.params.username)

const activeTab = ref('created')

// Mock user data
const user = ref({
  username: 'PhotoMaster',
  displayName: '<PERSON>',
  bio: 'Professional photographer and AI prompt creator. Specializing in portrait and landscape photography prompts for Midjourney.',
  location: 'San Francisco, CA',
  joinedDate: '2023-06-15',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  stats: {
    totalPrompts: 24,
    totalLikes: 3420,
    totalDownloads: 8950,
    followers: 1250
  },
  badges: [
    { name: 'Top Creator', color: 'bg-yellow-500', icon: '🏆' },
    { name: 'Photography Expert', color: 'bg-purple-500', icon: '📸' },
    { name: 'Community Favorite', color: 'bg-pink-500', icon: '❤️' }
  ]
})

// Mock prompts data
const createdPrompts = ref([
  {
    id: 1,
    title: "Professional Portrait Photography",
    content: "Create a professional headshot of a [profession] in [setting] with [lighting style]...",
    author: "PhotoMaster",
    model: "Midjourney",
    votes: 1247,
    thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop",
    tags: ["photography", "portrait", "professional"],
    isPremium: false
  },
  {
    id: 3,
    title: "Futuristic City Concept",
    content: "Design a futuristic cityscape with [architectural style] and [technology elements]...",
    author: "PhotoMaster",
    model: "Midjourney",
    votes: 756,
    thumbnail: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop",
    tags: ["architecture", "futuristic", "concept"],
    isPremium: false
  }
])

const likedPrompts = ref([
  {
    id: 2,
    title: "Creative Writing Assistant",
    content: "You are a creative writing assistant. Help me write a [genre] story about [topic]...",
    author: "WriterBot",
    model: "ChatGPT",
    votes: 892,
    thumbnail: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop",
    tags: ["writing", "creative", "storytelling"],
    isPremium: true,
    price: 4.99
  }
])

const purchasedPrompts = ref([
  {
    id: 4,
    title: "AI Video Script Generator",
    content: "Generate a compelling video script for [platform] about [topic] targeting [audience]...",
    author: "VideoCreator",
    model: "ChatGPT",
    votes: 234,
    thumbnail: "https://images.unsplash.com/photo-1536240478700-b869070f9279?w=300&h=200&fit=crop",
    tags: ["video", "script", "content"],
    isPremium: true,
    price: 2.99
  }
])

const currentPrompts = computed(() => {
  switch (activeTab.value) {
    case 'created':
      return createdPrompts.value
    case 'liked':
      return likedPrompts.value
    case 'purchased':
      return purchasedPrompts.value
    default:
      return []
  }
})

const tabs = [
  { id: 'created', label: 'Created Prompts', count: createdPrompts.value.length },
  { id: 'liked', label: 'Liked Prompts', count: likedPrompts.value.length },
  { id: 'purchased', label: 'Purchased Prompts', count: purchasedPrompts.value.length }
]
</script>

<template>
  <div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <!-- Profile Header -->
      <div class="glass-card p-8 mb-8">
        <div class="flex flex-col md:flex-row md:items-start md:space-x-8">
          <!-- Avatar -->
          <div class="flex-shrink-0 mb-6 md:mb-0">
            <img
              :src="user.avatar"
              :alt="user.displayName"
              class="w-32 h-32 rounded-full object-cover border-4 border-purple-500/30"
            />
          </div>
          
          <!-- User Info -->
          <div class="flex-1 space-y-4">
            <div>
              <h1 class="text-3xl font-sora font-bold text-white mb-2">
                {{ user.displayName }}
              </h1>
              <p class="text-purple-400 text-lg">@{{ user.username }}</p>
            </div>
            
            <p class="text-gray-300 leading-relaxed max-w-2xl">
              {{ user.bio }}
            </p>
            
            <!-- User Meta -->
            <div class="flex flex-wrap items-center gap-4 text-gray-400">
              <div class="flex items-center space-x-1">
                <MapPin class="w-4 h-4" />
                <span>{{ user.location }}</span>
              </div>
              <div class="flex items-center space-x-1">
                <Calendar class="w-4 h-4" />
                <span>Joined {{ new Date(user.joinedDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) }}</span>
              </div>
            </div>
            
            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-white">{{ user.stats.totalPrompts }}</div>
                <div class="text-gray-400 text-sm">Prompts</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-white">{{ user.stats.totalLikes.toLocaleString() }}</div>
                <div class="text-gray-400 text-sm">Likes</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-white">{{ user.stats.totalDownloads.toLocaleString() }}</div>
                <div class="text-gray-400 text-sm">Downloads</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-white">{{ user.stats.followers.toLocaleString() }}</div>
                <div class="text-gray-400 text-sm">Followers</div>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex flex-col space-y-3 mt-6 md:mt-0">
            <button class="glass-button">
              Follow
            </button>
            <button class="glass-button">
              Message
            </button>
          </div>
        </div>
        
        <!-- Badges -->
        <div class="mt-6 pt-6 border-t border-white/10">
          <h3 class="text-white font-medium mb-3 flex items-center space-x-2">
            <Award class="w-5 h-5 text-yellow-400" />
            <span>Achievements</span>
          </h3>
          <div class="flex flex-wrap gap-3">
            <div
              v-for="badge in user.badges"
              :key="badge.name"
              :class="[badge.color, 'px-3 py-1 rounded-full text-white text-sm font-medium flex items-center space-x-2']"
            >
              <span>{{ badge.icon }}</span>
              <span>{{ badge.name }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tabs -->
      <div class="glass-card mb-8">
        <div class="flex border-b border-white/10">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'px-6 py-4 font-medium transition-colors duration-200 border-b-2',
              activeTab === tab.id
                ? 'text-purple-400 border-purple-400'
                : 'text-gray-400 border-transparent hover:text-white'
            ]"
          >
            {{ tab.label }}
            <span class="ml-2 px-2 py-1 text-xs bg-white/10 rounded-full">
              {{ tab.count }}
            </span>
          </button>
        </div>
      </div>
      
      <!-- Prompts Grid -->
      <div v-if="currentPrompts.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <PromptCard
          v-for="prompt in currentPrompts"
          :key="prompt.id"
          :prompt="prompt"
        />
      </div>
      
      <!-- Empty State -->
      <div v-else class="glass-card p-12 text-center">
        <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <User class="w-8 h-8 text-gray-400" />
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No prompts found</h3>
        <p class="text-gray-400">
          {{ activeTab === 'created' ? 'This user hasn\'t created any prompts yet.' : 
             activeTab === 'liked' ? 'This user hasn\'t liked any prompts yet.' :
             'This user hasn\'t purchased any prompts yet.' }}
        </p>
      </div>
    </div>
  </div>
</template>
