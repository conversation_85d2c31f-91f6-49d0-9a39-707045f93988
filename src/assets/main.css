@import url("https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap");
@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-black text-white font-inter;
    background: radial-gradient(ellipse at top, rgba(120, 58, 237, 0.1) 0%, transparent 50%),
      radial-gradient(ellipse at bottom, rgba(34, 211, 238, 0.1) 0%, transparent 50%), #000000;
    min-height: 100vh;
  }
}

@layer components {
  .glass-card {
    @apply bg-glass-gradient backdrop-blur-md border border-white/10 rounded-xl;
  }

  .glass-button {
    @apply bg-glass-gradient backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white hover:bg-white/20 transition-all duration-300;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent;
  }

  .glow-effect {
    @apply shadow-lg shadow-purple-500/25;
  }

  .prompt-card {
    @apply glass-card p-6 hover:scale-105 transition-all duration-300 cursor-pointer;
  }

  .prompt-card:hover {
    @apply shadow-xl shadow-purple-500/30;
  }
}
