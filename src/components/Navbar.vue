<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink } from 'vue-router'
import { Search, Upload, User, Menu, X } from 'lucide-vue-next'

const isMenuOpen = ref(false)

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}
</script>

<template>
  <nav class="fixed top-0 left-0 right-0 z-50 glass-card border-b border-white/10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <RouterLink to="/" class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">P</span>
          </div>
          <span class="text-xl font-sora font-bold gradient-text">PromptRank</span>
        </RouterLink>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <RouterLink 
            to="/" 
            class="text-gray-300 hover:text-white transition-colors duration-200"
            active-class="text-white"
          >
            Explore
          </RouterLink>
          <RouterLink 
            to="/upload" 
            class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-1"
            active-class="text-white"
          >
            <Upload class="w-4 h-4" />
            <span>Upload</span>
          </RouterLink>
        </div>

        <!-- Search Bar -->
        <div class="hidden md:flex flex-1 max-w-lg mx-8">
          <div class="relative w-full">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search prompts..."
              class="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
        </div>

        <!-- User Menu -->
        <div class="hidden md:flex items-center space-x-4">
          <button class="glass-button">
            <User class="w-4 h-4" />
          </button>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMenu"
            class="text-gray-300 hover:text-white focus:outline-none focus:text-white"
          >
            <Menu v-if="!isMenuOpen" class="h-6 w-6" />
            <X v-else class="h-6 w-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="isMenuOpen" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 border-t border-white/10">
          <!-- Mobile Search -->
          <div class="relative mb-4">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search prompts..."
              class="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent backdrop-blur-sm"
            />
          </div>
          
          <RouterLink
            to="/"
            class="block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-200"
            active-class="text-white"
            @click="toggleMenu"
          >
            Explore
          </RouterLink>
          <RouterLink
            to="/upload"
            class="block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
            active-class="text-white"
            @click="toggleMenu"
          >
            <Upload class="w-4 h-4" />
            <span>Upload</span>
          </RouterLink>
          <button class="block w-full text-left px-3 py-2 text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2">
            <User class="w-4 h-4" />
            <span>Profile</span>
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>
