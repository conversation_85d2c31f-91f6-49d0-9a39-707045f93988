<script setup lang="ts">
import { computed } from 'vue'
import { RouterLink } from 'vue-router'
import { Heart, Copy, DollarSign, ArrowUp } from 'lucide-vue-next'

interface Prompt {
  id: number
  title: string
  content: string
  author: string
  model: string
  votes: number
  thumbnail: string
  tags: string[]
  isPremium: boolean
  price?: number
}

const props = defineProps<{
  prompt: Prompt
}>()

const modelColor = computed(() => {
  switch (props.prompt.model) {
    case 'ChatGPT':
      return 'bg-green-500'
    case 'Midjourney':
      return 'bg-purple-500'
    case 'Sora':
      return 'bg-blue-500'
    default:
      return 'bg-gray-500'
  }
})

const truncatedContent = computed(() => {
  return props.prompt.content.length > 120 
    ? props.prompt.content.substring(0, 120) + '...'
    : props.prompt.content
})
</script>

<template>
  <RouterLink 
    :to="`/prompt/${prompt.id}`"
    class="block prompt-card group"
  >
    <!-- Thumbnail -->
    <div class="relative mb-4 overflow-hidden rounded-lg">
      <img 
        :src="prompt.thumbnail" 
        :alt="prompt.title"
        class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
      />
      
      <!-- Model Badge -->
      <div class="absolute top-3 left-3">
        <span :class="[modelColor, 'px-2 py-1 text-xs font-medium text-white rounded-full']">
          {{ prompt.model }}
        </span>
      </div>
      
      <!-- Premium Badge -->
      <div v-if="prompt.isPremium" class="absolute top-3 right-3">
        <div class="flex items-center space-x-1 bg-yellow-500 px-2 py-1 rounded-full">
          <DollarSign class="w-3 h-3 text-white" />
          <span class="text-xs font-medium text-white">${{ prompt.price }}</span>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="space-y-3">
      <!-- Title -->
      <h3 class="text-lg font-sora font-semibold text-white group-hover:text-purple-300 transition-colors duration-200">
        {{ prompt.title }}
      </h3>
      
      <!-- Description -->
      <p class="text-gray-400 text-sm leading-relaxed">
        {{ truncatedContent }}
      </p>
      
      <!-- Tags -->
      <div class="flex flex-wrap gap-2">
        <span 
          v-for="tag in prompt.tags.slice(0, 3)" 
          :key="tag"
          class="px-2 py-1 text-xs bg-white/10 text-gray-300 rounded-md"
        >
          #{{ tag }}
        </span>
      </div>
      
      <!-- Footer -->
      <div class="flex items-center justify-between pt-3 border-t border-white/10">
        <!-- Author -->
        <div class="flex items-center space-x-2">
          <div class="w-6 h-6 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full flex items-center justify-center">
            <span class="text-xs font-bold text-white">{{ prompt.author[0] }}</span>
          </div>
          <span class="text-sm text-gray-400">{{ prompt.author }}</span>
        </div>
        
        <!-- Votes -->
        <div class="flex items-center space-x-1 text-gray-400">
          <ArrowUp class="w-4 h-4" />
          <span class="text-sm font-medium">{{ prompt.votes }}</span>
        </div>
      </div>
    </div>
    
    <!-- Hover Actions -->
    <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center space-x-4">
      <button 
        @click.prevent
        class="glass-button flex items-center space-x-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300"
      >
        <Heart class="w-4 h-4" />
        <span>Like</span>
      </button>
      <button 
        @click.prevent
        class="glass-button flex items-center space-x-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300"
        style="transition-delay: 0.1s"
      >
        <Copy class="w-4 h-4" />
        <span>Copy</span>
      </button>
    </div>
  </RouterLink>
</template>

<style scoped>
.prompt-card {
  position: relative;
}
</style>
