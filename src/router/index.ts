import { createRouter, createWebHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
    },
    {
      path: "/prompt/:id",
      name: "prompt-detail",
      component: () => import("../views/PromptDetailView.vue"),
    },
    {
      path: "/upload",
      name: "upload",
      component: () => import("../views/UploadPromptView.vue"),
    },
    {
      path: "/profile/:username",
      name: "profile",
      component: () => import("../views/UserProfileView.vue"),
    },
  ],
});

export default router;
