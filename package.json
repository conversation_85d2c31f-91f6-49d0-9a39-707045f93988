{"name": "prompt-ranking", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@tailwindcss/typography": "^0.5.16", "@vueuse/core": "^13.5.0", "autoprefixer": "^10.4.21", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^5.4.19", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}